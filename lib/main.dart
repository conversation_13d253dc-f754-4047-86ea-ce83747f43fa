import 'dart:developer';
import 'package:rapsap/services/firebaseservices.dart';
import 'package:rapsap/services/logging_service.dart';
import 'package:rapsap/services/logging_config.dart';
import 'package:rapsap/services/navigation_logger.dart';
import 'package:rapsap/view/widgets/keyboardhider.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:rapsap/view/widgets/notification_service.dart';
import 'package:flutter/services.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'view/widgets/commons.dart';
// ignore: unused_import
import 'package:firebase_performance/firebase_performance.dart';

void main() async {
  // 1️⃣ Ensure Flutter engine & platform channels are ready
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // 2️⃣ Firebase FIRST to avoid channel-error race conditions
    bool firebaseInitialized = await FirebaseService.init();
    if (firebaseInitialized) {
      log('✅ Firebase initialized successfully');
    } else {
      log('⚠ Firebase initialization failed, continuing without it');
    }

    // 3️⃣ Logging setup
    await LoggingService.instance.init();
    await LoggingConfig.instance.init();

    // 4️⃣ Local storage
    await GetStorage.init();
    await GetStorage.init('payload');

    // 5️⃣ High refresh rate (Android only)
    if (!kIsWeb) {
      try {
        await FlutterDisplayMode.setHighRefreshRate();
      } catch (e) {
        log('Display mode not supported: $e');
      }
    }

    // 6️⃣ Dependency bindings
    HomeBinding().dependencies();

    // 7️⃣ Lock orientation
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
    ]);

    // 8️⃣ Run the app
    runApp(const Rapsap());
  } catch (e, stackTrace) {
    log('❌ Error during app initialization: $e');
    log('Stack trace: $stackTrace');

    // Still try to run the app in case of errors
    runApp(const Rapsap());
  }
}

// Global storage
GetStorage storage = GetStorage();

class Rapsap extends StatelessWidget {
  const Rapsap({super.key});

  @override
  Widget build(BuildContext context) {
    return KeyboardHider(
      child: GetMaterialApp(
        onInit: () async {
          try {
            await NotificationService().setupInteractedMessage();
            log('📩 Notification interaction setup completed');
          } catch (e) {
            log('Error setting up notification interactions: $e');
          }
        },
        navigatorObservers: [
          ...FirebaseService.analyticsObserver,
          NavigationLogger(),
        ],
        defaultTransition: Transition.rightToLeft,
        initialBinding: HomeBinding(),
        theme: RapsapTheme.theme,
        debugShowCheckedModeBanner: false,
        home: const SplashScreen(),
      ),
    );
  }
}
