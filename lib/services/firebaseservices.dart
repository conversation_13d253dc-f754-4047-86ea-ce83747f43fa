import 'dart:developer';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_dynamic_links/firebase_dynamic_links.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:rapsap/firebase_options.dart';

import '../view/widgets/commons.dart';
import '../view/widgets/notification_service.dart';

class FirebaseService {
  // Private instances
  static FirebaseRemoteConfig? _remoteConfig;
  static FirebaseDynamicLinks? _dynamicLinks;
  static FirebaseAnalytics? _firebaseAnalytics;
  
  static bool _isInitialized = false;
  static bool _initializationInProgress = false;
  
  // Public getters with null checks
  static FirebaseRemoteConfig? get remoteConfig => _remoteConfig;
  static FirebaseDynamicLinks? get dynamicLinks => _dynamicLinks;
  static FirebaseAnalytics? get firebaseAnalytics => _firebaseAnalytics;
  
  static bool get isInitialized => _isInitialized;
  
  // Safe method to get analytics observer
  static List<NavigatorObserver> get analyticsObserver {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        return [FirebaseAnalyticsObserver(analytics: _firebaseAnalytics!)];
      } catch (e) {
        log('Error creating analytics observer: $e');
        return [];
      }
    }
    return [];
  }

  /// Initialize Firebase with comprehensive error handling and channel stability fixes
  static Future<bool> init() async {
    if (_isInitialized) {
      log('Firebase already initialized');
      return true;
    }

    if (_initializationInProgress) {
      log('Firebase initialization already in progress, waiting...');
      // Wait for current initialization to complete
      while (_initializationInProgress && !_isInitialized) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      return _isInitialized;
    }

    _initializationInProgress = true;

    try {
      // Check if Firebase is already initialized
      if (Firebase.apps.isNotEmpty) {
        log('Firebase app already exists');
        await _initializeInstances();
        _isInitialized = true;
        await _initializeServices();
        _initializationInProgress = false;
        return true;
      }

      // CRITICAL FIX: Ensure platform channels are fully ready
      // This addresses the channel-error issue with newer Flutter engines
      await _ensurePlatformChannelsReady();
      
      // Initialize Firebase with retry mechanism for channel stability
      bool initSuccess = await _initializeFirebaseWithRetry();
      
      if (!initSuccess) {
        _initializationInProgress = false;
        return false;
      }

      log('Firebase initialized successfully');
      
      // Initialize instances after successful Firebase initialization
      await _initializeInstances();
      _isInitialized = true;

      // Initialize services sequentially with error handling
      await _initializeServices();
      
      _initializationInProgress = false;
      return true;
      
    } catch (e) {
      log('Firebase initialization error: $e');
      _isInitialized = false;
      _initializationInProgress = false;
      
      // For critical errors, you might want to rethrow
      if (e.toString().contains('duplicate-app') || 
          e.toString().contains('invalid-api-key')) {
        rethrow;
      }
      
      // For other errors, return false but don't crash the app
      return false;
    }
  }

  /// CRITICAL FIX: Ensure platform channels are ready before Firebase initialization
  static Future<void> _ensurePlatformChannelsReady() async {
    try {
      // Extended wait for platform to be fully ready
      // This prevents the channel-error issue with newer Flutter engines
      await Future.delayed(const Duration(milliseconds: 500));

      // Test platform channel connectivity with multiple approaches
      if (!kIsWeb) {
        // Method 1: Test basic platform connectivity
        try {
          await SystemChannels.platform.invokeMethod('SystemSound.play', 'SystemSoundType.click')
              .timeout(const Duration(seconds: 3))
              .catchError((e) {
            log('🔧 Platform channel test 1 failed (may be expected): $e');
          });
        } catch (e) {
          log('🔧 Platform channel test 1 exception: $e');
        }

        // Method 2: Additional platform readiness check using MethodChannel
        try {
          const MethodChannel('flutter/platform')
              .invokeMethod('SystemChrome.setEnabledSystemUIMode')
              .timeout(const Duration(seconds: 2))
              .catchError((e) {
            log('🔧 Platform channel test 2 failed (may be expected): $e');
          });
        } catch (e) {
          log('🔧 Platform channel test 2 exception: $e');
        }

        // Additional stability delay
        await Future.delayed(const Duration(milliseconds: 300));
      }

      log('🔧 Platform channels readiness check completed');
    } catch (e) {
      log('🔧 Platform channel readiness check error: $e');
      // Even if checks fail, continue - this is defensive programming
    }
  }

  /// Initialize Firebase with enhanced retry mechanism for channel stability
  static Future<bool> _initializeFirebaseWithRetry() async {
    int maxRetries = 5; // Increased retries for better reliability
    int retryDelay = 1000; // Start with longer delay

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        log('🔥 Firebase initialization attempt $attempt/$maxRetries');

        // Additional platform readiness check before each attempt
        if (attempt > 1) {
          log('🔥 Additional platform readiness check before retry...');
          await _ensurePlatformChannelsReady();
        }

        await Firebase.initializeApp(
          options: DefaultFirebaseOptions.currentPlatform,
        );

        // Verify initialization was successful
        if (Firebase.apps.isNotEmpty) {
          log('✅ Firebase initialization successful on attempt $attempt');
          return true;
        }

      } catch (e) {
        log('❌ Firebase initialization attempt $attempt failed: $e');

        // Handle specific error types
        String errorString = e.toString().toLowerCase();

        // For duplicate-app errors, consider it a success
        if (errorString.contains('duplicate-app')) {
          log('✅ Firebase app already exists (duplicate-app error)');
          return true;
        }

        // If this is a channel error and we have retries left, try again
        if ((errorString.contains('channel-error') ||
             errorString.contains('unable to establish connection')) &&
            attempt < maxRetries) {
          log('🔄 Channel error detected, retrying in ${retryDelay}ms...');
          await Future.delayed(Duration(milliseconds: retryDelay));
          retryDelay = (retryDelay * 1.5).round(); // Gradual backoff
          continue;
        }

        // For critical errors, fail immediately
        if (errorString.contains('invalid-api-key') ||
            errorString.contains('project not found')) {
          log('💥 Critical Firebase configuration error: $e');
          rethrow;
        }

        // If this is the last attempt, rethrow
        if (attempt == maxRetries) {
          log('💥 Firebase initialization failed after $maxRetries attempts');
          rethrow;
        }
      }
    }

    return false;
  }

  /// Initialize Firebase service instances with enhanced error handling
  static Future<void> _initializeInstances() async {
    try {
      // Only initialize if Firebase is properly set up
      if (Firebase.apps.isEmpty) {
        throw Exception('Firebase apps not available');
      }

      // Initialize with additional safety checks
      try {
        _firebaseAnalytics = FirebaseAnalytics.instance;
        log('Firebase Analytics initialized');
      } catch (e) {
        log('Firebase Analytics initialization failed: $e');
        _firebaseAnalytics = null;
      }

      try {
        _remoteConfig = FirebaseRemoteConfig.instance;
        log('Firebase Remote Config initialized');
      } catch (e) {
        log('Firebase Remote Config initialization failed: $e');
        _remoteConfig = null;
      }
      
      // Dynamic Links might not be available on all platforms
      try {
        _dynamicLinks = FirebaseDynamicLinks.instance;
        log('Firebase Dynamic Links initialized');
      } catch (e) {
        log('Dynamic Links not available: $e');
        _dynamicLinks = null;
      }
      
      log('Firebase instances initialization completed');
    } catch (e) {
      log('Error initializing Firebase instances: $e');
      rethrow;
    }
  }

  /// Initialize Firebase services with improved error isolation
  static Future<void> _initializeServices() async {
    try {
      // Initialize notification service only on supported platforms
      if (!kIsWeb) {
        try {
          await NotificationService().init();
          log('Notification service initialized');
        } catch (e) {
          log('Notification service initialization failed: $e');
          // Continue with other services even if notifications fail
        }
      }

      // Initialize remote config with error isolation
      try {
        await _initializeRemoteConfig();
        log('Remote config initialized');
      } catch (e) {
        log('Remote config initialization failed: $e');
        // Continue with other services
      }
      
      // Initialize crashlytics with error isolation
      try {
        _initializeCrashlytics();
        log('Crashlytics initialized');
      } catch (e) {
        log('Crashlytics initialization failed: $e');
        // Continue - crashlytics failure shouldn't stop the app
      }
      
    } catch (e) {
      log('Service initialization error: $e');
      // Don't rethrow here to avoid crashing the app
    }
  }

  /// Initialize Crashlytics with enhanced error handling
  static void _initializeCrashlytics() {
    try {
      if (!kDebugMode && !kIsWeb && _isInitialized) {
        FlutterError.onError = (errorDetails) {
          try {
            FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
          } catch (e) {
            log('Failed to record flutter error to crashlytics: $e');
          }
        };
        
        PlatformDispatcher.instance.onError = (error, stack) {
          try {
            FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
          } catch (e) {
            log('Failed to record error to crashlytics: $e');
          }
          return true;
        };
        
        log('Crashlytics error handlers set up successfully');
      }
    } catch (e) {
      log('Crashlytics setup error: $e');
    }
  }

  /// Initialize Remote Config with enhanced error handling
  static Future<void> _initializeRemoteConfig() async {
    if (!_isInitialized || _remoteConfig == null) {
      log('Firebase not initialized or remote config unavailable');
      return;
    }
    
    try {
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(hours: 1),
      ));
      log('Remote config settings applied successfully');
    } catch (e) {
      log('Remote config setup error: $e');
    }
  }

  // Backwards compatibility methods with null checks
  static void crashlytics() {
    _initializeCrashlytics();
  }

  static Future<void> remoteconfig() async {
    await _initializeRemoteConfig();
  }

  /// Safe method to get analytics observer (alternative to getter)
  static List<NavigatorObserver> getAnalyticsObserver() {
    return analyticsObserver;
  }

  /// Safe method to log analytics events
  static Future<void> logEvent(String name, {Map<String, Object>? parameters}) async {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        await _firebaseAnalytics!.logEvent(name: name, parameters: parameters);
      } catch (e) {
        log('Error logging analytics event: $e');
      }
    }
  }

  /// Safe method to set user ID
  static Future<void> setUserId(String userId) async {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        await _firebaseAnalytics!.setUserId(id: userId);
      } catch (e) {
        log('Error setting user ID: $e');
      }
    }
  }

  /// Safe method to log app open
  static Future<void> logAppOpen() async {
    if (_isInitialized && _firebaseAnalytics != null) {
      try {
        await _firebaseAnalytics!.logAppOpen();
      } catch (e) {
        log('Error logging app open: $e');
      }
    }
  }

  /// Get dynamic link (safely)
  static Future<PendingDynamicLinkData?> getInitialLink() async {
    if (_isInitialized && _dynamicLinks != null) {
      try {
        return await _dynamicLinks!.getInitialLink();
      } catch (e) {
        log('Error getting initial dynamic link: $e');
        return null;
      }
    }
    return null;
  }

  /// Handle dynamic link (safely)
  static void handleDynamicLinks(Function(PendingDynamicLinkData) onLink) {
    if (_isInitialized && _dynamicLinks != null) {
      try {
        _dynamicLinks!.onLink.listen(onLink, onError: (error) {
          log('Dynamic link error: $error');
        });
      } catch (e) {
        log('Error setting up dynamic link handler: $e');
      }
    }
  }
}